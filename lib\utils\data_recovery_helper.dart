import 'dart:io';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import '../models/novel.dart';

/// 数据恢复助手
/// 帮助恢复因数据结构变更而丢失的小说数据
class DataRecoveryHelper {
  
  /// 检查是否存在备份的Hive文件
  static Future<List<String>> checkBackupFiles() async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final hiveDir = Directory(documentsDir.path);
      
      final backupFiles = <String>[];
      
      // 查找可能的备份文件
      await for (final entity in hiveDir.list()) {
        if (entity is File) {
          final fileName = entity.path.split(Platform.pathSeparator).last;
          
          // 查找novels相关的文件
          if (fileName.contains('novels') && 
              (fileName.endsWith('.hive') || fileName.endsWith('.bak'))) {
            backupFiles.add(entity.path);
          }
        }
      }
      
      return backupFiles;
    } catch (e) {
      print('检查备份文件失败: $e');
      return [];
    }
  }
  
  /// 尝试从备份文件恢复数据
  static Future<List<Novel>> tryRecoverFromBackup(String backupPath) async {
    try {
      print('尝试从备份文件恢复: $backupPath');
      
      // 创建临时盒子来读取备份数据
      final tempBoxName = 'temp_recovery_${DateTime.now().millisecondsSinceEpoch}';
      
      // 复制备份文件到临时位置
      final backupFile = File(backupPath);
      final documentsDir = await getApplicationDocumentsDirectory();
      final tempFile = File('${documentsDir.path}/$tempBoxName.hive');
      
      await backupFile.copy(tempFile.path);
      
      // 尝试打开临时盒子
      final tempBox = await Hive.openBox(tempBoxName);
      
      final recoveredNovels = <Novel>[];
      
      // 遍历所有键，尝试恢复小说数据
      for (final key in tempBox.keys) {
        if (key.toString().startsWith('novel_')) {
          try {
            final data = tempBox.get(key);
            if (data is Novel) {
              recoveredNovels.add(data);
              print('成功恢复小说: ${data.title}');
            }
          } catch (e) {
            print('恢复小说失败 (键: $key): $e');
          }
        }
      }
      
      // 清理临时文件
      await tempBox.close();
      await tempFile.delete();
      
      return recoveredNovels;
      
    } catch (e) {
      print('从备份恢复失败: $e');
      return [];
    }
  }
  
  /// 检查文件系统中是否有小说文件
  static Future<List<String>> checkFileSystemNovels() async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final novelsDir = Directory('${documentsDir.path}/novels');
      
      final novelFolders = <String>[];
      
      if (await novelsDir.exists()) {
        await for (final entity in novelsDir.list()) {
          if (entity is Directory) {
            // 检查是否包含章节文件
            final chapterFiles = <String>[];
            await for (final file in entity.list()) {
              if (file is File && file.path.endsWith('.md')) {
                chapterFiles.add(file.path);
              }
            }
            
            if (chapterFiles.isNotEmpty) {
              novelFolders.add(entity.path);
            }
          }
        }
      }
      
      return novelFolders;
    } catch (e) {
      print('检查文件系统小说失败: $e');
      return [];
    }
  }
  
  /// 从文件系统恢复小说
  static Future<Novel?> recoverFromFileSystem(String novelFolderPath) async {
    try {
      final novelDir = Directory(novelFolderPath);
      final novelName = novelDir.path.split(Platform.pathSeparator).last;
      
      // 读取大纲文件
      final outlineFile = File('${novelDir.path}/outline.md');
      String outline = '';
      if (await outlineFile.exists()) {
        outline = await outlineFile.readAsString();
      }
      
      // 读取所有章节文件
      final chapters = <Chapter>[];
      final chapterFiles = <File>[];
      
      await for (final entity in novelDir.list()) {
        if (entity is File && entity.path.endsWith('.md') && 
            !entity.path.endsWith('outline.md')) {
          chapterFiles.add(entity);
        }
      }
      
      // 按文件名排序
      chapterFiles.sort((a, b) => a.path.compareTo(b.path));
      
      for (int i = 0; i < chapterFiles.length; i++) {
        final file = chapterFiles[i];
        final content = await file.readAsString();
        final fileName = file.path.split(Platform.pathSeparator).last;
        final chapterTitle = fileName.replaceAll('.md', '');
        
        chapters.add(Chapter(
          number: i + 1,
          title: chapterTitle,
          content: content,
        ));
      }
      
      if (chapters.isNotEmpty) {
        return Novel(
          title: novelName,
          genre: '未分类',
          outline: outline,
          content: chapters.map((c) => c.content).join('\n\n'),
          chapters: chapters,
          createdAt: DateTime.now(),
          folderPath: novelFolderPath,
          useFileSystem: true,
        );
      }
      
      return null;
    } catch (e) {
      print('从文件系统恢复小说失败: $e');
      return null;
    }
  }
  
  /// 生成恢复报告
  static Future<String> generateRecoveryReport() async {
    final report = StringBuffer();
    report.writeln('=== 数据恢复报告 ===\n');
    
    // 检查备份文件
    final backupFiles = await checkBackupFiles();
    report.writeln('备份文件检查:');
    if (backupFiles.isEmpty) {
      report.writeln('  未找到备份文件');
    } else {
      for (final file in backupFiles) {
        report.writeln('  找到备份: $file');
      }
    }
    report.writeln();
    
    // 检查文件系统小说
    final novelFolders = await checkFileSystemNovels();
    report.writeln('文件系统小说检查:');
    if (novelFolders.isEmpty) {
      report.writeln('  未找到文件系统小说');
    } else {
      for (final folder in novelFolders) {
        report.writeln('  找到小说文件夹: $folder');
      }
    }
    
    return report.toString();
  }
}
