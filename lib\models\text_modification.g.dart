// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_modification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextModification _$TextModificationFromJson(Map<String, dynamic> json) =>
    TextModification(
      id: json['id'] as String,
      type: ModificationType.values.firstWhere(
        (e) => e.toString() == 'ModificationType.${json['type']}',
        orElse: () => ModificationType.replace,
      ),
      startLine: json['startLine'] as int,
      endLine: json['endLine'] as int,
      originalText: json['originalText'] as String,
      newText: json['newText'] as String,
      reason: json['reason'] as String,
      status: ModificationStatus.values.firstWhere(
        (e) => e.toString() == 'ModificationStatus.${json['status']}',
        orElse: () => ModificationStatus.pending,
      ),
    );

Map<String, dynamic> _$TextModificationToJson(TextModification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type.toString().split('.').last,
      'startLine': instance.startLine,
      'endLine': instance.endLine,
      'originalText': instance.originalText,
      'newText': instance.newText,
      'reason': instance.reason,
      'status': instance.status.toString().split('.').last,
    };

CreativeEditResponse _$CreativeEditResponseFromJson(
        Map<String, dynamic> json) =>
    CreativeEditResponse(
      responseType: json['responseType'] as String,
      summary: json['summary'] as String,
      modifications: (json['modifications'] as List<dynamic>)
          .map((e) => TextModification.fromJson(e as Map<String, dynamic>))
          .toList(),
      overallExplanation: json['overallExplanation'] as String,
    );

Map<String, dynamic> _$CreativeEditResponseToJson(
        CreativeEditResponse instance) =>
    <String, dynamic>{
      'responseType': instance.responseType,
      'summary': instance.summary,
      'modifications': instance.modifications,
      'overallExplanation': instance.overallExplanation,
    };
