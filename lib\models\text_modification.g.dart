// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_modification.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ModificationTypeAdapter extends TypeAdapter<ModificationType> {
  @override
  final int typeId = 20;

  @override
  ModificationType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ModificationType.replace;
      case 1:
        return ModificationType.insert;
      case 2:
        return ModificationType.delete;
      default:
        return ModificationType.replace;
    }
  }

  @override
  void write(BinaryWriter writer, ModificationType obj) {
    switch (obj) {
      case ModificationType.replace:
        writer.writeByte(0);
        break;
      case ModificationType.insert:
        writer.writeByte(1);
        break;
      case ModificationType.delete:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModificationTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ModificationStatusAdapter extends TypeAdapter<ModificationStatus> {
  @override
  final int typeId = 22;

  @override
  ModificationStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ModificationStatus.pending;
      case 1:
        return ModificationStatus.accepted;
      case 2:
        return ModificationStatus.rejected;
      case 3:
        return ModificationStatus.applied;
      default:
        return ModificationStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, ModificationStatus obj) {
    switch (obj) {
      case ModificationStatus.pending:
        writer.writeByte(0);
        break;
      case ModificationStatus.accepted:
        writer.writeByte(1);
        break;
      case ModificationStatus.rejected:
        writer.writeByte(2);
        break;
      case ModificationStatus.applied:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModificationStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TextModificationAdapter extends TypeAdapter<TextModification> {
  @override
  final int typeId = 21;

  @override
  TextModification read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TextModification(
      id: fields[0] as String,
      type: fields[1] as ModificationType,
      startLine: fields[2] as int,
      endLine: fields[3] as int,
      originalText: fields[4] as String,
      newText: fields[5] as String,
      reason: fields[6] as String,
      status: fields[7] as ModificationStatus,
    );
  }

  @override
  void write(BinaryWriter writer, TextModification obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.startLine)
      ..writeByte(3)
      ..write(obj.endLine)
      ..writeByte(4)
      ..write(obj.originalText)
      ..writeByte(5)
      ..write(obj.newText)
      ..writeByte(6)
      ..write(obj.reason)
      ..writeByte(7)
      ..write(obj.status);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TextModificationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CreativeEditResponseAdapter extends TypeAdapter<CreativeEditResponse> {
  @override
  final int typeId = 23;

  @override
  CreativeEditResponse read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CreativeEditResponse(
      responseType: fields[0] as String,
      summary: fields[1] as String,
      modifications: (fields[2] as List).cast<TextModification>(),
      overallExplanation: fields[3] as String,
    );
  }

  @override
  void write(BinaryWriter writer, CreativeEditResponse obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.responseType)
      ..writeByte(1)
      ..write(obj.summary)
      ..writeByte(2)
      ..write(obj.modifications)
      ..writeByte(3)
      ..write(obj.overallExplanation);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CreativeEditResponseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextModification _$TextModificationFromJson(Map<String, dynamic> json) =>
    TextModification(
      id: json['id'] as String,
      type: $enumDecode(_$ModificationTypeEnumMap, json['type']),
      startLine: json['startLine'] as int,
      endLine: json['endLine'] as int,
      originalText: json['originalText'] as String,
      newText: json['newText'] as String,
      reason: json['reason'] as String,
      status: $enumDecode(_$ModificationStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$TextModificationToJson(TextModification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ModificationTypeEnumMap[instance.type]!,
      'startLine': instance.startLine,
      'endLine': instance.endLine,
      'originalText': instance.originalText,
      'newText': instance.newText,
      'reason': instance.reason,
      'status': _$ModificationStatusEnumMap[instance.status]!,
    };

const _$ModificationTypeEnumMap = {
  ModificationType.replace: 'replace',
  ModificationType.insert: 'insert',
  ModificationType.delete: 'delete',
};

const _$ModificationStatusEnumMap = {
  ModificationStatus.pending: 'pending',
  ModificationStatus.accepted: 'accepted',
  ModificationStatus.rejected: 'rejected',
};

CreativeEditResponse _$CreativeEditResponseFromJson(
        Map<String, dynamic> json) =>
    CreativeEditResponse(
      responseType: json['responseType'] as String,
      summary: json['summary'] as String,
      modifications: (json['modifications'] as List<dynamic>)
          .map((e) => TextModification.fromJson(e as Map<String, dynamic>))
          .toList(),
      overallExplanation: json['overallExplanation'] as String,
    );

Map<String, dynamic> _$CreativeEditResponseToJson(
        CreativeEditResponse instance) =>
    <String, dynamic>{
      'responseType': instance.responseType,
      'summary': instance.summary,
      'modifications': instance.modifications,
      'overallExplanation': instance.overallExplanation,
    };
