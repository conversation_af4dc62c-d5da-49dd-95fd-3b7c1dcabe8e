﻿import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/novel_agent_controller.dart';
import '../controllers/novel_controller.dart';
import '../models/novel.dart';
import '../widgets/novel_agent_panel.dart';
import '../services/version_control_service.dart';

class DaizongAIAssistantScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const DaizongAIAssistantScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<DaizongAIAssistantScreen> createState() => _DaizongAIAssistantScreenState();
}

class _DaizongAIAssistantScreenState extends State<DaizongAIAssistantScreen> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  final NovelController _novelController = Get.find<NovelController>();
  final VersionControlService _versionControl = Get.put(VersionControlService());

  final List<TextEditingController> _chapterControllers = [];
  final List<TextEditingController> _chapterTitleControllers = [];
  final ScrollController _editorScrollController = ScrollController();

  late Novel _currentNovel;
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  double _aiPanelWidth = 400;

  // 用于记录编辑前的状态
  String? _lastSavedTitle;
  String? _lastSavedContent;

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel;
    _currentChapterIndex = widget.initialChapterIndex ?? 0;
    
    _initChapterControllers();
    _initAgent();
  }

  void _initChapterControllers() {
    // 清空现有控制器
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.dispose();
    }

    _chapterControllers.clear();
    _chapterTitleControllers.clear();

    print('初始化控制器，章节数量: ${_currentNovel.chapters.length}');

    for (int i = 0; i < _currentNovel.chapters.length; i++) {
      final chapter = _currentNovel.chapters[i];

      // 内容控制器
      final contentController = TextEditingController(text: chapter.content);
      contentController.addListener(_onTextChanged);
      _chapterControllers.add(contentController);

      // 标题控制器
      final titleController = TextEditingController(text: chapter.title);
      titleController.addListener(_onTitleChanged);
      _chapterTitleControllers.add(titleController);

      print('初始化第${i}章控制器: ${chapter.title}');
    }

    print('控制器初始化完成 - 内容控制器: ${_chapterControllers.length}, 标题控制器: ${_chapterTitleControllers.length}');
  }

  void _initAgent() async {
    await _agentController.setCurrentNovel(_currentNovel);

    if (_currentNovel.chapters.isNotEmpty &&
        _currentChapterIndex < _currentNovel.chapters.length) {
      _agentController.currentChapter.value = _currentNovel.chapters[_currentChapterIndex];

      // 设置初始的最后保存状态
      _lastSavedTitle = _currentNovel.chapters[_currentChapterIndex].title;
      _lastSavedContent = _currentNovel.chapters[_currentChapterIndex].content;
    }

    // 监听章节内容变化
    _agentController.currentChapter.listen((chapter) {
      if (chapter != null && _currentChapterIndex < _chapterControllers.length) {
        // 更新编辑器内容，但不触发 _onTextChanged
        _chapterControllers[_currentChapterIndex].removeListener(_onTextChanged);
        _chapterControllers[_currentChapterIndex].text = chapter.content;
        _chapterControllers[_currentChapterIndex].addListener(_onTextChanged);
      }
    });
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  void _onTitleChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  /// 记录编辑操作到版本控制系统
  Future<void> _recordEditOperation() async {
    if (_currentChapterIndex < _chapterControllers.length &&
        _currentChapterIndex < _chapterTitleControllers.length) {

      final currentTitle = _chapterTitleControllers[_currentChapterIndex].text;
      final currentContent = _chapterControllers[_currentChapterIndex].text;

      await _versionControl.recordOperation(
        chapterIndex: _currentChapterIndex,
        oldTitle: _lastSavedTitle,
        newTitle: currentTitle,
        oldContent: _lastSavedContent,
        newContent: currentContent,
      );

      // 更新最后保存的状态
      _lastSavedTitle = currentTitle;
      _lastSavedContent = currentContent;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(48), // 减少AppBar高度
        child: AppBar(
          title: Text(
            '《${_currentNovel.title}》- 岱宗AI辅助助手',
            style: const TextStyle(fontSize: 16), // 减小标题字体
          ),
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          elevation: 1, // 减少阴影
          actions: [
            // 撤销按钮
            Obx(() => IconButton(
              icon: const Icon(Icons.undo, size: 20),
              onPressed: _versionControl.canUndo.value ? _undoLastOperation : null,
              tooltip: '撤销',
            )),
            // 重做按钮
            Obx(() => IconButton(
              icon: const Icon(Icons.redo, size: 20),
              onPressed: _versionControl.canRedo.value ? _redoLastOperation : null,
              tooltip: '重做',
            )),
            // 创建快照按钮
            IconButton(
              icon: const Icon(Icons.camera_alt, size: 20),
              onPressed: _showCreateSnapshotDialog,
              tooltip: '创建快照',
            ),
            // 查看快照历史按钮
            IconButton(
              icon: const Icon(Icons.history, size: 20),
              onPressed: _showSnapshotHistoryDialog,
              tooltip: '快照历史',
            ),
            // 保存按钮
            IconButton(
              icon: const Icon(Icons.save, size: 20), // 减小图标大小
              onPressed: _hasChanges ? _saveChanges : null,
              tooltip: '保存',
            ),
          ],
        ),
      ),
      body: Row(
        children: [
          // 左侧编辑器区域
          Expanded(
            child: _buildEditor(),
          ),

          // 可拖拽的分隔条
          _buildResizableHandle(context),

          // 右侧AI面板区域
          Container(
            width: _aiPanelWidth,
            child: NovelAgentPanel(
              novel: _currentNovel,
              width: _aiPanelWidth,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建可拖拽的分隔条
  Widget _buildResizableHandle(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.resizeColumn,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _aiPanelWidth = (_aiPanelWidth - details.delta.dx).clamp(300.0, 600.0);
          });
        },
        child: Container(
          width: 4, // 减少分隔条宽度从8px到4px
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 1, // 减少分隔线宽度从2px到1px
              color: Theme.of(context).dividerColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '章节：',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _currentNovel.chapters.asMap().entries.map((entry) {
                  final index = entry.key;
                  final chapter = entry.value;
                  final isSelected = index == _currentChapterIndex;

                  // 调试信息
                  print('章节调试: index=$index, number=${chapter.number}, title=${chapter.title}');

                  return Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: ChoiceChip(
                      label: Text(
                        '第${chapter.number}章',
                        style: const TextStyle(fontSize: 12),
                      ),
                      selected: isSelected,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                      onSelected: (selected) {
                        if (selected) {
                          _switchToChapter(index);
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return const Center(
        child: Text('暂无章节内容'),
      );
    }

    if (_currentChapterIndex >= _chapterControllers.length ||
        _currentChapterIndex >= _chapterTitleControllers.length) {
      return const Center(
        child: Text('章节索引超出范围'),
      );
    }

    // 添加调试信息
    print('构建编辑器内容 - 当前章节索引: $_currentChapterIndex');
    print('章节数量: ${_currentNovel.chapters.length}');
    print('内容控制器数量: ${_chapterControllers.length}');
    print('标题控制器数量: ${_chapterTitleControllers.length}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 可编辑的章节标题
        Container(
          height: 40, // 收窄高度
          child: Row(
            children: [
              Text(
                '第${_currentNovel.chapters[_currentChapterIndex].number}章：',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              Expanded(
                child: _currentChapterIndex < _chapterTitleControllers.length
                    ? TextField(
                        controller: _chapterTitleControllers[_currentChapterIndex],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintText: '章节标题',
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          isDense: true,
                        ),
                        maxLines: 1,
                      )
                    : Text(
                        _currentNovel.chapters[_currentChapterIndex].title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8), // 减少间距
        Expanded(
          child: TextField(
            controller: _chapterControllers[_currentChapterIndex],
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编辑章节内容...',
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
            ),
          ),
        ),
      ],
    );
  }

  void _switchToChapter(int index) {
    if (index >= 0 && index < _currentNovel.chapters.length) {
      setState(() {
        _currentChapterIndex = index;
      });

      // 更新 Agent 控制器的当前章节
      _agentController.currentChapter.value = _currentNovel.chapters[index];

      // 同步编辑器内容和标题
      if (index < _chapterControllers.length && index < _currentNovel.chapters.length) {
        _chapterControllers[index].removeListener(_onTextChanged);
        _chapterControllers[index].text = _currentNovel.chapters[index].content;
        _chapterControllers[index].addListener(_onTextChanged);
      }

      if (index < _chapterTitleControllers.length && index < _currentNovel.chapters.length) {
        _chapterTitleControllers[index].removeListener(_onTitleChanged);
        _chapterTitleControllers[index].text = _currentNovel.chapters[index].title;
        _chapterTitleControllers[index].addListener(_onTitleChanged);
      }

      // 更新最后保存的状态
      _lastSavedTitle = _currentNovel.chapters[index].title;
      _lastSavedContent = _currentNovel.chapters[index].content;
    }
  }

  void _saveChanges() async {
    try {
      // 记录编辑操作到版本控制系统
      await _recordEditOperation();

      // 保存章节内容和标题
      for (int i = 0; i < _chapterControllers.length; i++) {
        if (i < _currentNovel.chapters.length) {
          // 更新内容
          _currentNovel.chapters[i].content = _chapterControllers[i].text;

          // 更新标题（使用copyWith创建新实例）
          if (i < _chapterTitleControllers.length) {
            final newTitle = _chapterTitleControllers[i].text;
            if (newTitle != _currentNovel.chapters[i].title) {
              _currentNovel.chapters[i] = _currentNovel.chapters[i].copyWith(
                title: newTitle,
                content: _chapterControllers[i].text,
              );
            }
          }
        }
      }

      await _novelController.updateNovel(_currentNovel);

      setState(() {
        _hasChanges = false;
      });

      Get.snackbar(
        '保存成功',
        '章节内容已保存',
        snackPosition: SnackPosition.BOTTOM,
      );

    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存章节内容时出错: $e',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }

  /// 撤销最后一次操作
  void _undoLastOperation() {
    final operation = _versionControl.undo();
    if (operation != null) {
      _applyOperation(operation, isUndo: true);
    }
  }

  /// 重做最后一次撤销的操作
  void _redoLastOperation() {
    final operation = _versionControl.redo();
    if (operation != null) {
      _applyOperation(operation, isUndo: false);
    }
  }

  /// 应用操作（撤销或重做）
  void _applyOperation(EditOperation operation, {required bool isUndo}) {
    if (operation.chapterIndex != _currentChapterIndex) {
      _switchToChapter(operation.chapterIndex);
    }

    // 暂时移除监听器以避免触发新的操作记录
    _chapterControllers[_currentChapterIndex].removeListener(_onTextChanged);
    _chapterTitleControllers[_currentChapterIndex].removeListener(_onTitleChanged);

    try {
      if (isUndo) {
        // 撤销：恢复到旧状态
        if (operation.oldTitle != null) {
          _chapterTitleControllers[_currentChapterIndex].text = operation.oldTitle!;
        }
        if (operation.oldContent != null) {
          _chapterControllers[_currentChapterIndex].text = operation.oldContent!;
        }
      } else {
        // 重做：应用新状态
        if (operation.newTitle != null) {
          _chapterTitleControllers[_currentChapterIndex].text = operation.newTitle!;
        }
        if (operation.newContent != null) {
          _chapterControllers[_currentChapterIndex].text = operation.newContent!;
        }
      }

      setState(() {
        _hasChanges = true;
      });

      Get.snackbar(
        isUndo ? '撤销成功' : '重做成功',
        '已${isUndo ? '撤销' : '重做'}${operation.operationType == 'title_change' ? '标题' : operation.operationType == 'content_change' ? '内容' : '标题和内容'}修改',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

    } finally {
      // 重新添加监听器
      _chapterControllers[_currentChapterIndex].addListener(_onTextChanged);
      _chapterTitleControllers[_currentChapterIndex].addListener(_onTitleChanged);
    }
  }

  /// 显示创建快照对话框
  void _showCreateSnapshotDialog() {
    final noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建快照'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('为第${_currentNovel.chapters[_currentChapterIndex].number}章创建快照'),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: '快照备注',
                hintText: '请输入快照说明...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _createSnapshot(noteController.text.trim());
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }

  /// 创建快照
  Future<void> _createSnapshot(String note) async {
    try {
      if (note.isEmpty) {
        note = '快照 - ${DateTime.now().toString().split('.')[0]}';
      }

      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      final currentTitle = _chapterTitleControllers[_currentChapterIndex].text;
      final currentContent = _chapterControllers[_currentChapterIndex].text;

      await _versionControl.createSnapshot(
        novelId: _currentNovel.id,
        chapterNumber: currentChapter.number,
        title: currentTitle,
        content: currentContent,
        note: note,
      );

      Get.snackbar(
        '快照创建成功',
        '已为第${currentChapter.number}章创建快照',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

    } catch (e) {
      Get.snackbar(
        '创建快照失败',
        '创建快照时出错: $e',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }

  /// 显示快照历史对话框
  void _showSnapshotHistoryDialog() {
    final currentChapter = _currentNovel.chapters[_currentChapterIndex];
    final snapshots = _versionControl.getChapterSnapshots(_currentNovel.id, currentChapter.number);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('第${currentChapter.number}章快照历史'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: snapshots.isEmpty
              ? const Center(
                  child: Text('暂无快照'),
                )
              : ListView.builder(
                  itemCount: snapshots.length,
                  itemBuilder: (context, index) {
                    final snapshot = snapshots[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        title: Text(
                          snapshot.note,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('创建时间: ${snapshot.createdAt.toString().split('.')[0]}'),
                            Text('标题: ${snapshot.title}'),
                            Text('内容长度: ${snapshot.content.length}字'),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.restore),
                              onPressed: () {
                                Navigator.pop(context);
                                _restoreFromSnapshot(snapshot);
                              },
                              tooltip: '恢复到此快照',
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () {
                                _deleteSnapshot(snapshot);
                                Navigator.pop(context);
                                _showSnapshotHistoryDialog(); // 刷新对话框
                              },
                              tooltip: '删除快照',
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 从快照恢复
  void _restoreFromSnapshot(ChapterSnapshot snapshot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认恢复'),
        content: Text('确定要恢复到快照"${snapshot.note}"吗？\n\n当前未保存的修改将会丢失。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performRestore(snapshot);
            },
            child: const Text('确认恢复'),
          ),
        ],
      ),
    );
  }

  /// 执行恢复操作
  void _performRestore(ChapterSnapshot snapshot) async {
    // 记录当前状态到版本控制
    await _recordEditOperation();

    // 暂时移除监听器
    _chapterControllers[_currentChapterIndex].removeListener(_onTextChanged);
    _chapterTitleControllers[_currentChapterIndex].removeListener(_onTitleChanged);

    try {
      // 恢复内容
      _chapterTitleControllers[_currentChapterIndex].text = snapshot.title;
      _chapterControllers[_currentChapterIndex].text = snapshot.content;

      setState(() {
        _hasChanges = true;
      });

      Get.snackbar(
        '恢复成功',
        '已恢复到快照"${snapshot.note}"',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

    } finally {
      // 重新添加监听器
      _chapterControllers[_currentChapterIndex].addListener(_onTextChanged);
      _chapterTitleControllers[_currentChapterIndex].addListener(_onTitleChanged);
    }
  }

  /// 删除快照
  Future<void> _deleteSnapshot(ChapterSnapshot snapshot) async {
    try {
      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      await _versionControl.deleteSnapshot(_currentNovel.id, currentChapter.number, snapshot.id);

      Get.snackbar(
        '删除成功',
        '快照"${snapshot.note}"已删除',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

    } catch (e) {
      Get.snackbar(
        '删除失败',
        '删除快照时出错: $e',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.dispose();
    }
    _editorScrollController.dispose();
    super.dispose();
  }
}
