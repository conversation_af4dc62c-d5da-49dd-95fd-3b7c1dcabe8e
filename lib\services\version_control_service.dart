import 'package:get/get.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/novel.dart';

/// 版本控制服务
/// 管理章节编辑的撤销/重做和快照功能
class VersionControlService extends GetxService {
  static const String _snapshotsKey = 'chapter_snapshots';
  static const String _operationsKey = 'edit_operations';

  late SharedPreferences _prefs;
  
  // 操作历史栈
  final List<EditOperation> _undoStack = [];
  final List<EditOperation> _redoStack = [];
  
  // 响应式状态
  final RxBool canUndo = false.obs;
  final RxBool canRedo = false.obs;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    try {
      _prefs = await SharedPreferences.getInstance();
    } catch (e) {
      print('初始化版本控制存储失败: $e');
    }
  }
  
  /// 记录编辑操作
  Future<void> recordOperation({
    required int chapterIndex,
    String? oldTitle,
    String? newTitle,
    String? oldContent,
    String? newContent,
  }) async {
    // 确定操作类型
    String operationType = 'content_change';
    if (oldTitle != newTitle && oldContent != newContent) {
      operationType = 'both';
    } else if (oldTitle != newTitle) {
      operationType = 'title_change';
    }
    
    final operation = EditOperation(
      chapterIndex: chapterIndex,
      oldTitle: oldTitle,
      newTitle: newTitle,
      oldContent: oldContent,
      newContent: newContent,
      timestamp: DateTime.now(),
      operationType: operationType,
    );
    
    // 添加到撤销栈
    _undoStack.add(operation);
    
    // 清空重做栈
    _redoStack.clear();
    
    // 限制撤销栈大小
    if (_undoStack.length > 50) {
      _undoStack.removeAt(0);
    }
    
    // 保存到持久化存储
    await _saveOperations();

    _updateButtonStates();
  }
  
  /// 撤销操作
  EditOperation? undo() {
    if (_undoStack.isEmpty) return null;
    
    final operation = _undoStack.removeLast();
    _redoStack.add(operation);
    
    _updateButtonStates();
    return operation;
  }
  
  /// 重做操作
  EditOperation? redo() {
    if (_redoStack.isEmpty) return null;
    
    final operation = _redoStack.removeLast();
    _undoStack.add(operation);
    
    _updateButtonStates();
    return operation;
  }
  
  /// 更新按钮状态
  void _updateButtonStates() {
    canUndo.value = _undoStack.isNotEmpty;
    canRedo.value = _redoStack.isNotEmpty;
  }
  
  /// 创建章节快照
  Future<String> createSnapshot({
    required String novelId,
    required int chapterNumber,
    required String title,
    required String content,
    required String note,
  }) async {
    final snapshot = ChapterSnapshot(
      chapterNumber: chapterNumber.toString(),
      title: title,
      content: content,
      createdAt: DateTime.now(),
      note: note,
    );

    // 获取现有快照
    final snapshots = await _getSnapshots();
    final key = '${novelId}_${chapterNumber}_${snapshot.id}';
    snapshots[key] = snapshot.toJson();

    // 保存到SharedPreferences
    await _prefs.setString(_snapshotsKey, jsonEncode(snapshots));

    return snapshot.id;
  }
  
  /// 获取章节快照列表
  List<ChapterSnapshot> getChapterSnapshots(String novelId, int chapterNumber) {
    final prefix = '${novelId}_${chapterNumber}_';
    final snapshots = <ChapterSnapshot>[];

    try {
      final snapshotsData = _prefs.getString(_snapshotsKey);
      if (snapshotsData != null) {
        final Map<String, dynamic> allSnapshots = jsonDecode(snapshotsData);

        for (final entry in allSnapshots.entries) {
          if (entry.key.startsWith(prefix)) {
            final snapshot = ChapterSnapshot.fromJson(entry.value);
            snapshots.add(snapshot);
          }
        }
      }
    } catch (e) {
      print('获取快照列表失败: $e');
    }

    // 按创建时间倒序排列
    snapshots.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return snapshots;
  }
  
  /// 删除快照
  Future<void> deleteSnapshot(String novelId, int chapterNumber, String snapshotId) async {
    try {
      final snapshots = await _getSnapshots();
      final key = '${novelId}_${chapterNumber}_$snapshotId';
      snapshots.remove(key);
      await _prefs.setString(_snapshotsKey, jsonEncode(snapshots));
    } catch (e) {
      print('删除快照失败: $e');
    }
  }
  
  /// 清空操作历史
  void clearHistory() {
    _undoStack.clear();
    _redoStack.clear();
    _updateButtonStates();
  }
  
  /// 获取操作历史统计
  Map<String, int> getHistoryStats() {
    int totalSnapshots = 0;
    try {
      final snapshotsData = _prefs.getString(_snapshotsKey);
      if (snapshotsData != null) {
        final Map<String, dynamic> allSnapshots = jsonDecode(snapshotsData);
        totalSnapshots = allSnapshots.length;
      }
    } catch (e) {
      print('获取快照统计失败: $e');
    }

    return {
      'undoCount': _undoStack.length,
      'redoCount': _redoStack.length,
      'totalSnapshots': totalSnapshots,
    };
  }

  /// 获取快照数据
  Future<Map<String, dynamic>> _getSnapshots() async {
    try {
      final snapshotsData = _prefs.getString(_snapshotsKey);
      if (snapshotsData != null) {
        return jsonDecode(snapshotsData);
      }
    } catch (e) {
      print('获取快照数据失败: $e');
    }
    return {};
  }

  /// 保存操作历史
  Future<void> _saveOperations() async {
    try {
      final operations = _undoStack.map((op) => op.toJson()).toList();
      await _prefs.setString(_operationsKey, jsonEncode(operations));
    } catch (e) {
      print('保存操作历史失败: $e');
    }
  }
}
